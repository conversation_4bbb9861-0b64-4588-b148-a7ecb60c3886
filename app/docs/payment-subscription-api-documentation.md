# Payment Subscription API Documentation

This document describes the Payment Subscription APIs that integrate with LemonSqueezy for handling subscription plans, checkout sessions, and user payment status.

## Base URL

- **Development**: `https://dapi-test.adscloud.org:8443`
- **Production**: `https://dapi.adscloud.org`

## Authentication

Most endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### 1. Get Payment Plans

**Endpoint**: `GET /api/auth/payment/plans`  
**Authentication**: Not required  
**Description**: Fetch all available subscription plans

#### Response
```json
{
  "success": true,
  "message": "Subscription plans retrieved successfully",
  "data": {
    "plans": [
      {
        "id": "free",
        "name": "Free",
        "description": "Free plan for everyone",
        "price": "Free",
        "features": ["Basic support", "1 queue"],
        "effect": {
          "type": "recurrent",
          "kind": "subscription",
          "amount": 30,
          "queues": 1
        },
        "isSubscription": true,
        "isOneTime": false
      },
      {
        "id": "hobby",
        "name": "Starter",
        "description": "All you need to get started",
        "price": "€9.99",
        "features": ["Basic support", "3 queues"],
        "effect": {
          "type": "recurrent",
          "kind": "subscription",
          "amount": 200,
          "queues": 3
        },
        "isSubscription": true,
        "isOneTime": false
      },
      {
        "id": "pro",
        "name": "Business",
        "description": "Our most popular plan",
        "price": "€19.99",
        "features": ["Priority customer support", "10 queues"],
        "effect": {
          "type": "recurrent",
          "kind": "subscription",
          "amount": 1000,
          "queues": 10
        },
        "isSubscription": true,
        "isOneTime": false
      },
      {
        "id": "credits10",
        "name": "100 Credits",
        "description": "One-time purchase of 100 credits for your account",
        "price": "€4.99",
        "features": ["No expiration date"],
        "effect": {
          "type": "one-time",
          "kind": "credits",
          "amount": 100,
          "queues": null
        },
        "isSubscription": false,
        "isOneTime": true
      }
    ]
  }
}
```

### 2. Get User Payment Status

**Endpoint**: `GET /api/auth/payment/status`  
**Authentication**: Required  
**Description**: Get current user's subscription status and credits

#### Response
```json
{
  "success": true,
  "message": "User subscription status retrieved successfully",
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "credits": 150,
      "queues": 3,
      "datePaid": "2024-01-15T10:30:00.000Z"
    },
    "subscription": {
      "isActive": true,
      "status": "active",
      "planId": "hobby",
      "planName": "Starter",
      "planDetails": {
        "effect": {
          "type": "recurrent",
          "kind": "subscription",
          "amount": 200,
          "queues": 3
        },
        "features": ["Basic support", "3 queues"]
      }
    },
    "hasCustomerPortal": true
  }
}
```

### 3. Create Checkout Session

**Endpoint**: `POST /api/auth/payment/checkout`  
**Authentication**: Required  
**Description**: Create a checkout session for subscribing to a plan

#### Request Body
```json
{
  "planId": "hobby"
}
```

#### Response
```json
{
  "success": true,
  "message": "Checkout session created successfully",
  "data": {
    "sessionUrl": "https://checkout.lemonsqueezy.com/...",
    "sessionId": "session-id",
    "planId": "hobby",
    "planName": "Starter"
  }
}
```

### 4. Get Customer Portal URL

**Endpoint**: `GET /api/auth/payment/customer-portal`  
**Authentication**: Required  
**Description**: Get customer portal URL for subscription management

#### Response
```json
{
  "success": true,
  "message": "Customer portal URL retrieved successfully",
  "data": {
    "customerPortalUrl": "https://portal.lemonsqueezy.com/..."
  }
}
```

#### Error Response (No Portal Available)
```json
{
  "success": false,
  "message": "Customer portal not available. You need to have an active subscription first."
}
```

### 5. Get Usage Statistics

**Endpoint**: `GET /api/auth/payment/usage`  
**Authentication**: Required  
**Description**: Get user's current usage statistics and limits

#### Query Parameters
- `period` (optional): `week`, `month`, or `year` (default: `month`)

#### Response
```json
{
  "success": true,
  "message": "Usage statistics retrieved successfully",
  "data": {
    "period": "month",
    "current": {
      "credits": 150,
      "queues": 3
    },
    "limits": {
      "credits": 200,
      "queues": 3
    },
    "subscription": {
      "planId": "hobby",
      "planName": "Starter",
      "status": "active",
      "isActive": true
    }
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description"
}
```

### Common HTTP Status Codes

- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `404` - Not Found
- `500` - Internal Server Error

## Plan Types

### Subscription Plans
- **Free**: €0/month - 30 credits, 1 queue
- **Starter (Hobby)**: €9.99/month - 200 credits, 3 queues  
- **Business (Pro)**: €19.99/month - 1000 credits, 10 queues

### One-time Purchases
- **100 Credits**: €4.99 - 100 credits, no expiration

## Subscription Statuses

- `active` - Subscription is active and user has access
- `past_due` - Payment failed, user may have limited access
- `cancel_at_period_end` - Subscription will cancel at end of current period
- `deleted` - Subscription has been cancelled/expired

## Integration Notes

1. **Checkout Flow**: Use the checkout session URL to redirect users to LemonSqueezy payment page
2. **Webhooks**: LemonSqueezy webhooks automatically update user subscription status
3. **Customer Portal**: Users can manage their subscriptions through the LemonSqueezy customer portal
4. **Credits**: Credits are automatically added when subscriptions are activated or one-time purchases are completed

## Example Usage

### JavaScript/TypeScript Example

```javascript
// Get available plans
const plansResponse = await fetch('/api/auth/payment/plans');
const plans = await plansResponse.json();

// Get user status
const statusResponse = await fetch('/api/auth/payment/status', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const userStatus = await statusResponse.json();

// Create checkout session
const checkoutResponse = await fetch('/api/auth/payment/checkout', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({ planId: 'hobby' })
});
const checkout = await checkoutResponse.json();

// Redirect to checkout
if (checkout.success) {
  window.location.href = checkout.data.sessionUrl;
}
```

### cURL Examples

```bash
# Get plans
curl -X GET "https://dapi-test.adscloud.org:8443/api/auth/payment/plans"

# Get user status
curl -X GET "https://dapi-test.adscloud.org:8443/api/auth/payment/status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Create checkout session
curl -X POST "https://dapi-test.adscloud.org:8443/api/auth/payment/checkout" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"planId": "hobby"}'
```
