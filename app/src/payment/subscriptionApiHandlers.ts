import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { generateCheckoutSession } from 'wasp/server/operations';
import { PaymentPlanId, paymentPlans, prettyPaymentPlanName, SubscriptionStatus, paymentPlanCards } from './plans';
import { paymentProcessor } from './paymentProcessor';
import * as z from 'zod';

// Validation schemas
const checkoutSessionSchema = z.object({
  planId: z.nativeEnum(PaymentPlanId)
});

const usageQuerySchema = z.object({
  period: z.enum(['week', 'month', 'year']).optional().default('month')
});

type CheckoutSessionInput = z.infer<typeof checkoutSessionSchema>;
type UsageQueryInput = z.infer<typeof usageQuerySchema>;

/**
 * GET /api/auth/subscription/plans
 * Fetch all available subscription plans
 */
export const handleGetSubscriptionPlans = async (req: Request, res: Response, context: any) => {
  try {
    const plans = Object.entries(paymentPlans).map(([planId, plan]) => ({
      id: planId,
      name: prettyPaymentPlanName(planId as PaymentPlanId),
      description: paymentPlanCards[planId as PaymentPlanId].description,
      price: paymentPlanCards[planId as PaymentPlanId].price,
      features: paymentPlanCards[planId as PaymentPlanId].features,
      effect: plan.effect,
      isSubscription: plan.effect.type === 'recurrent',
      isOneTime: plan.effect.type === 'one-time'
    }));

    res.status(200).json({
      success: true,
      message: 'Subscription plans retrieved successfully',
      data: {
        plans
      }
    });
  } catch (error: any) {
    console.error('Error fetching subscription plans:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch subscription plans'
    });
  }
};

/**
 * GET /api/auth/subscription/status
 * Get current user's subscription status and credits
 */
export const handleGetUserSubscriptionStatus = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: {
        id: true,
        email: true,
        subscriptionStatus: true,
        subscriptionPlan: true,
        credits: true,
        queues: true,
        datePaid: true,
        paymentProcessorUserId: true,
        lemonSqueezyCustomerPortalUrl: true
      }
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    const isSubscribed = !!user.subscriptionStatus && user.subscriptionStatus !== SubscriptionStatus.Deleted;
    const currentPlan = user.subscriptionPlan ? paymentPlans[user.subscriptionPlan as PaymentPlanId] : null;

    res.status(200).json({
      success: true,
      message: 'User subscription status retrieved successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          credits: user.credits,
          queues: user.queues,
          datePaid: user.datePaid
        },
        subscription: {
          isActive: isSubscribed,
          status: user.subscriptionStatus,
          planId: user.subscriptionPlan,
          planName: user.subscriptionPlan ? prettyPaymentPlanName(user.subscriptionPlan as PaymentPlanId) : null,
          planDetails: currentPlan ? {
            effect: currentPlan.effect,
            features: paymentPlanCards[user.subscriptionPlan as PaymentPlanId]?.features || []
          } : null
        },
        hasCustomerPortal: !!user.lemonSqueezyCustomerPortalUrl
      }
    });
  } catch (error: any) {
    console.error('Error fetching user subscription status:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch subscription status'
    });
  }
};

/**
 * POST /api/auth/subscription/checkout
 * Create checkout session for a subscription plan
 */
export const handleCreateCheckoutSession = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const validatedBody = checkoutSessionSchema.safeParse(req.body);
    if (!validatedBody.success) {
      const errorDetails = validatedBody.error.issues.map(issue => 
        `${issue.path.join('.')} - ${issue.message}`
      ).join('; ');
      throw new HttpError(400, `Invalid request body: ${errorDetails}`);
    }

    const { planId } = validatedBody.data;

    // Use existing generateCheckoutSession operation
    const checkoutSession = await generateCheckoutSession(planId, context);

    res.status(200).json({
      success: true,
      message: 'Checkout session created successfully',
      data: {
        sessionUrl: checkoutSession.sessionUrl,
        sessionId: checkoutSession.sessionId,
        planId,
        planName: prettyPaymentPlanName(planId)
      }
    });
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to create checkout session'
    });
  }
};

/**
 * GET /api/auth/subscription/customer-portal
 * Get customer portal URL for subscription management
 */
export const handleGetCustomerPortalUrl = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    // Use payment processor directly to get customer portal URL
    const customerPortalUrl = await paymentProcessor.fetchCustomerPortalUrl({
      userId: context.user.id,
      prismaUserDelegate: context.entities.User,
    });

    if (!customerPortalUrl) {
      return res.status(404).json({
        success: false,
        message: 'Customer portal not available. You need to have an active subscription first.'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Customer portal URL retrieved successfully',
      data: {
        customerPortalUrl
      }
    });
  } catch (error: any) {
    console.error('Error fetching customer portal URL:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch customer portal URL'
    });
  }
};

/**
 * GET /api/auth/payment/credits/used
 * Get user's used credits for the current month (completed appointments)
 */
export const handleGetUsedCredits = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    // Calculate current month date range
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    // Count completed appointments for this month
    // Assuming appointments are linked to users through CustomerFolder
    const completedAppointments = await context.entities.Appointment.count({
      where: {
        customerFolder: {
          userId: context.user.id
        },
        status: 'completed',
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      }
    });

    // Get user's current credit balance
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: {
        credits: true,
        subscriptionPlan: true
      }
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    // Calculate total credits allocated this month (from subscription plan)
    const currentPlan = user.subscriptionPlan ? paymentPlans[user.subscriptionPlan as PaymentPlanId] : null;
    const monthlyCreditsAllocated = currentPlan?.effect.kind === 'subscription' ? currentPlan.effect.amount : 0;

    res.status(200).json({
      success: true,
      message: 'Used credits retrieved successfully',
      data: {
        period: {
          month: now.getMonth() + 1,
          year: now.getFullYear(),
          startDate: startOfMonth.toISOString(),
          endDate: endOfMonth.toISOString()
        },
        credits: {
          used: completedAppointments,
          remaining: user.credits,
          monthlyAllocated: monthlyCreditsAllocated,
          totalAvailable: user.credits + completedAppointments
        },
        appointments: {
          completedThisMonth: completedAppointments
        },
        subscription: {
          planId: user.subscriptionPlan,
          planName: user.subscriptionPlan ? prettyPaymentPlanName(user.subscriptionPlan as PaymentPlanId) : null
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching used credits:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch used credits'
    });
  }
};

/**
 * GET /api/auth/subscription/usage
 * Get user's current usage statistics and limits
 */
export const handleGetUsageStats = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const validatedQuery = usageQuerySchema.safeParse(req.query);
    if (!validatedQuery.success) {
      throw new HttpError(400, 'Invalid query parameters');
    }

    const { period } = validatedQuery.data;

    // Get user's current limits
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: {
        credits: true,
        queues: true,
        subscriptionPlan: true,
        subscriptionStatus: true
      }
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;
    
    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    // Get usage statistics (you might want to add more specific queries based on your needs)
    const currentPlan = user.subscriptionPlan ? paymentPlans[user.subscriptionPlan as PaymentPlanId] : null;
    const planLimits = currentPlan ? {
      credits: currentPlan.effect.amount,
      queues: currentPlan.effect.queues || 1
    } : {
      credits: 0,
      queues: 1
    };

    res.status(200).json({
      success: true,
      message: 'Usage statistics retrieved successfully',
      data: {
        period,
        current: {
          credits: user.credits,
          queues: user.queues
        },
        limits: planLimits,
        subscription: {
          planId: user.subscriptionPlan,
          planName: user.subscriptionPlan ? prettyPaymentPlanName(user.subscriptionPlan as PaymentPlanId) : null,
          status: user.subscriptionStatus,
          isActive: !!user.subscriptionStatus && user.subscriptionStatus !== SubscriptionStatus.Deleted
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching usage statistics:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch usage statistics'
    });
  }
};
